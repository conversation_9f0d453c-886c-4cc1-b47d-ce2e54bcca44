import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
    const { theme, toggleTheme } = useTheme();

    return (
        <Tooltip 
            title={theme === 'light' ? 'Switch to Dark Side' : 'Join the Light Side'} 
            arrow
        >
            <IconButton
                onClick={toggleTheme}
                className="theme-toggle"
                aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
                style={{
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    zIndex: 1000,
                    width: '56px',
                    height: '56px',
                    borderRadius: '50%',
                    background: theme === 'light' 
                        ? 'rgba(26, 26, 46, 0.9)' 
                        : 'rgba(240, 240, 240, 0.9)',
                    backdropFilter: 'blur(10px)',
                    border: theme === 'light' 
                        ? '2px solid #0066ff' 
                        : '2px solid #00ff41',
                    boxShadow: theme === 'light'
                        ? '0 0 20px rgba(0, 102, 255, 0.3)'
                        : '0 0 20px rgba(0, 255, 65, 0.3)',
                    transition: 'all 0.3s ease',
                }}
            >
                <div
                    style={{
                        fontSize: '24px',
                        transition: 'all 0.3s ease',
                        filter: theme === 'light' 
                            ? 'drop-shadow(0 0 8px rgba(0, 102, 255, 0.8))'
                            : 'drop-shadow(0 0 8px rgba(0, 255, 65, 0.8))',
                    }}
                >
                    {theme === 'light' ? '🌙' : '☀️'}
                </div>
            </IconButton>
        </Tooltip>
    );
};

export default ThemeToggle;
