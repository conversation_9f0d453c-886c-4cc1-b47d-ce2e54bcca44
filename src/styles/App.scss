@import url("https://fonts.googleapis.com/css2?family=Fira+Code:wght@500&family=JetBrains+Mono:wght@600&family=Orbitron:wght@400;700;900&family=Space+Mono:wght@400;700&display=swap");

// CSS Variables for theming
:root {
    // Dark theme (Return of the Jedi Luke) - Default
    --bg-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    --bg-secondary: rgba(26, 26, 46, 0.8);
    --text-primary: #e0e0e0;
    --text-secondary: #e0e0e0;
    --accent-primary: #00ff41;
    --accent-secondary: #FFD700;
    --border-color: rgba(0, 255, 65, 0.3);
    --shadow-color: rgba(0, 255, 65, 0.2);
    --starfield-opacity: 0.2;
}

[data-theme="light"] {
    // Light theme (A New Hope Luke) - Improved colors
    --bg-primary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    --bg-secondary: rgba(248, 249, 250, 0.95);
    --text-primary: #1a1a1a;
    --text-secondary: #343a40;
    --accent-primary: #0066cc;
    --accent-secondary: #ffc107;
    --border-color: rgba(0, 102, 204, 0.4);
    --shadow-color: rgba(0, 102, 204, 0.3);
    --starfield-opacity: 0.08;

    // Light mode specific overrides
    --light-card-bg: rgba(255, 255, 255, 0.9);
    --light-card-border: rgba(0, 102, 204, 0.2);
    --light-text-glow: rgba(26, 26, 26, 0.1);
    --light-accent-glow: rgba(0, 102, 204, 0.4);
}

// Light mode specific improvements
[data-theme="light"] {
    .App {
        // Improve starfield visibility in light mode
        &::before {
            background-image:
                radial-gradient(1px 1px at 20px 30px, var(--accent-primary), transparent),
                radial-gradient(1px 1px at 40px 70px, var(--text-secondary), transparent),
                radial-gradient(1px 1px at 90px 40px, var(--accent-primary), transparent),
                radial-gradient(1px 1px at 130px 80px, var(--text-secondary), transparent),
                radial-gradient(1px 1px at 160px 30px, var(--accent-primary), transparent);
            opacity: 0.05; // Very subtle in light mode
        }
    }

    // Improve text readability in light mode
    .name b {
        color: var(--text-primary);
        text-shadow: 0 0 5px var(--light-text-glow);
    }

    .special {
        color: var(--accent-primary);
        text-shadow: 0 0 8px var(--light-accent-glow);
    }
}

.App {
    text-align: center;
    font-family: "Space Mono", "JetBrains Mono", monospace;
    font-size: calc(1em + 1vw);
    margin: 0;
    margin-top: 0px;
    padding: 0;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    background: var(--bg-primary);
    position: relative;
    overflow-x: hidden;
    transition: background 0.3s ease;

    // Optimized starfield background (static for better performance)
    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(1px 1px at 20px 30px, var(--accent-primary), transparent),
            radial-gradient(1px 1px at 40px 70px, var(--text-primary), transparent),
            radial-gradient(1px 1px at 90px 40px, var(--accent-primary), transparent),
            radial-gradient(1px 1px at 130px 80px, var(--text-primary), transparent),
            radial-gradient(1px 1px at 160px 30px, var(--accent-primary), transparent);
        background-repeat: repeat;
        background-size: 300px 200px;
        z-index: -1;
        opacity: var(--starfield-opacity);
        transition: opacity 0.3s ease;
        will-change: auto; // Remove will-change for better performance
    }

    header {
        padding: 0 !important;
    }

    p {
        color: var(--text-primary);
        text-shadow: 0 0 5px var(--shadow-color);
        transition: color 0.3s ease;
    }

    .nav {
        display: grid;
        margin-bottom: 1vh;
    }

    .bar {
        position: fixed;
        top: 50%;
        right: 0;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .bar a {
        display: block;
        text-align: center;
        padding: 16px;
        transition: all 0.3s ease;
        color: white;
        opacity: 25%;
    }

    .bar a:hover {
        opacity: 100%;
    }

    .nav-wrapper {
        position: fixed;
        top: 0;
        right: 0;
        text-align: right;
        font-size: 1rem;
        z-index: 1;
        margin-right: 5vw;
        margin-top: 5vh;

        a {
            margin: 4px;
        }
    }

    .nav-wrapper-mobile {
        padding-left: 3vw;
        text-align: right;
        top: 0;
        right: 0;
        margin-bottom: 2em;
        font-size: 1em;
        margin-top: 1em;

        a {
            margin: 4px;
        }

        hr {
            height: 0.05em;
            width: 60vw;
            background-color: rgb(27, 226, 27);
            margin-top: 10vw;
        }
    }

    .fade-in-section {
        opacity: 0;
        transform: translateY(10vh);
        visibility: hidden;
        transition: opacity 0.6s ease-out, transform 1s ease-out;
        will-change: opacity, visibility;
    }
    .fade-in-section.is-visible {
        opacity: 1;
        transform: none;
        visibility: visible;
    }

    a {
        text-decoration: none;
        color: var(--accent-primary);
        text-shadow: 0 0 5px var(--shadow-color);
        transition: color 0.2s ease, text-shadow 0.2s ease;
    }

    a:hover {
        color: var(--accent-secondary);
        text-shadow: 0 0 8px var(--shadow-color);
    }

    .name {
        text-align: left;
        margin-top: 1rem;
        font-size: clamp(2rem, 3em, 6rem);
        margin-bottom: 0;
        font-family: "Orbitron", "Space Mono", monospace;
        font-weight: 700;

        b {
            color: var(--text-primary);
            text-shadow: 0 0 10px var(--shadow-color);
        }
    }

    .special {
        color: var(--accent-primary);
        text-shadow: 0 0 10px var(--shadow-color);
        transition: color 0.3s ease, text-shadow 0.3s ease;
    }

    .closing-symbol {
        margin: 0 auto;
        font-size: 1.2em;
    }

    .guideArrow {
        font-size: 3rem;
        display: block;
        border-radius: 1rem !important;
        color: var(--accent-primary);
        top: 50%;
        left: 40%;
        transition: color 0.3s ease;
    }

    .display-pic {
        margin-top: 1.5rem;
        width: 20rem;
        height: 20rem;
        border-radius: 1rem;
    }

    .headline {
        margin-top: 0;
        text-align: left;
        font-size: clamp(0.5rem, 1.3em, 3.5rem);
        color: var(--text-primary);
        font-family: "Space Mono", monospace;
        text-shadow: 0 0 10px var(--shadow-color);
        line-height: 1.4;
        transition: color 0.3s ease, text-shadow 0.3s ease;
    }

    .call-to-action-button {
        height: 3em;
        width: 10em;
        font-size: 1em;
        border: 2px solid var(--accent-primary);
        font-family: "Orbitron", "JetBrains Mono", monospace;
        color: var(--accent-primary);
        text-transform: none;
        position: relative;
        background: var(--bg-secondary);
        border-radius: 8px;
        box-shadow: 0 0 10px var(--shadow-color);
        transition: color 0.2s ease, background 0.2s ease, box-shadow 0.2s ease;
    }

    .call-to-action-button:hover {
        color: var(--bg-primary);
        background: var(--accent-primary);
        box-shadow: 0 0 15px var(--shadow-color);
    }

    .sub-heading {
        font-family: "Orbitron", "Fira Mono", monospace;
        margin-top: 1rem;
        margin-bottom: 1rem;
        text-align: left;
        font-size: clamp(0.7rem, 1.05em, 3rem);
        color: var(--text-primary);
        font-weight: 700;
        text-shadow: 0 0 10px var(--shadow-color);
        transition: color 0.3s ease, text-shadow 0.3s ease;
    }

    .sub-heading::after {
        display: inline-block;
        height: 2px;
        width: 300px;
        margin-left: 1vw;
        background: linear-gradient(90deg, var(--accent-primary), transparent);
        box-shadow: 0 0 10px var(--shadow-color);
        content: "";
        transition: background 0.3s ease, box-shadow 0.3s ease;
    }

    .list-item {
        font-size: clamp(0.5rem, 1.1rem, 1.5rem);
        color: var(--text-primary);
        text-shadow: 0 0 5px var(--shadow-color);
        transition: color 0.3s ease, text-shadow 0.3s ease;
    }

    .resume-button {
        color: var(--accent-primary);
        border: 2px solid var(--accent-primary);
        background: var(--bg-secondary);
        font-family: "Orbitron", monospace;
        box-shadow: 0 0 15px var(--shadow-color);
        transition: all 0.3s ease;
    }

    .resume-button:hover {
        color: var(--bg-primary);
        background: var(--accent-primary);
        box-shadow: 0 0 25px var(--shadow-color);
        transform: translateY(-2px);
    }

    .socials {
        margin-top: 1rem;
        position: relative;
        text-align: center;
        height: 1rem;

        .favicon {
            color: var(--accent-primary);
            margin: 0.5rem;
            height: 2rem;
            width: 1.3em;
            transition: all 0.5s ease;
            filter: drop-shadow(0 0 10px var(--shadow-color));
        }

        .favicon:hover {
            text-decoration: none;
            transform: scale(1.3);
            color: var(--accent-secondary);
            filter: drop-shadow(0 0 15px var(--shadow-color));
        }
    }

    .intro {
        margin-top: 20vh;
        margin-bottom: 50vh;
        width: 60vw;
        margin-left: 0;
    }

    .intro-mobile {
        margin-top: 5rem;
        margin-bottom: 5rem;
    }

    .section {
        margin-top: 5vh;
        margin-bottom: 5vh;
    }

    .section-mobile {
        margin-top: 5vh;
        margin-bottom: 5vh;
    }

    .skillsGrid {
        margin: 0 auto;
        width: 100%;

        .skill {
            height: 2.5rem;
            width: 2.5rem;
            fill: var(--accent-primary);
            margin: 1em;
            transition: transform 0.3s ease, fill 0.3s ease;
            filter: drop-shadow(0 0 5px var(--shadow-color));
        }

        .skill:hover {
            transform: scale(1.2);
            fill: var(--accent-secondary);
            filter: drop-shadow(0 0 8px var(--shadow-color));
        }
    }

    .projectGrid {
        margin: 0 auto;
        gap: 2rem !important; // Increase spacing between cards

        .project {
            margin: 1rem; // Add proper margin around each project
            display: flex;
            justify-content: center;

            .project-card {
                height: 17rem;
                border-radius: 1rem;
                width: 20rem;
                background: var(--bg-secondary);
                backdrop-filter: blur(5px);
                border: 1px solid var(--border-color);
                color: var(--text-primary);
                box-shadow: 0 0 15px var(--shadow-color);
                transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease, background 0.2s ease;
                position: relative;
                z-index: 1;
                will-change: transform;

                &:hover {
                    border-color: var(--accent-primary);
                    box-shadow: 0 0 20px var(--shadow-color);
                    transform: translateY(-3px);
                    z-index: 2;
                }

                // Light mode specific styling
                [data-theme="light"] & {
                    background: var(--light-card-bg);
                    border: 1px solid var(--light-card-border);
                    box-shadow: 0 0 15px var(--light-accent-glow);

                    &:hover {
                        box-shadow: 0 0 20px var(--light-accent-glow);
                    }
                }

                // Ensure clickable areas work properly
                .MuiCardActionArea-root {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                }

                button {
                    width: 100%;
                    height: 100%;

                    span {
                        height: 0px !important;
                    }
                }

                h1 {
                    font-family: "Orbitron", "Fira Mono", monospace;
                    color: var(--accent-primary);
                    text-shadow: 0 0 10px var(--shadow-color);
                    margin-bottom: 0.5rem;
                    transition: color 0.3s ease, text-shadow 0.3s ease;
                }

                p {
                    font-size: 0.8rem;
                    font-family: "Space Mono", "JetBrains Mono", monospace;
                    color: var(--text-primary);
                    text-shadow: 0 0 5px var(--shadow-color);
                    transition: color 0.3s ease, text-shadow 0.3s ease;
                }

                // Ensure links are clickable and styled properly
                a {
                    color: var(--accent-primary) !important;
                    text-shadow: 0 0 5px var(--shadow-color) !important;
                    transition: color 0.2s ease, text-shadow 0.2s ease !important;
                    pointer-events: auto !important;
                    z-index: 10 !important;
                    position: relative !important;

                    &:hover {
                        color: var(--accent-secondary) !important;
                        text-shadow: 0 0 8px var(--shadow-color) !important;
                    }
                }
            }
        }
    }
}

.contactContainer {
    text-align: left;
    font-family: "Space Mono", "JetBrains Mono", monospace;
}

.footer-container {
    font-size: 1rem;
    flex: 1;
    padding: 1rem 0;
    justify-content: center;
    align-items: center;
    width: 100vw;
    background-color: aba7a7;

    .sub-heading::after {
        height: 0px;
    }

    .section {
        margin-top: 5rem;
        margin-bottom: 0;
    }

    .footer {
        display: flex;
        justify-content: center;
        align-items: center;

        p {
            color: grey;
        }

        a {
            color: darkgrey;
            text-decoration: none;
            background-image: linear-gradient(grey, grey);
            background-size: 0% 0.1em;
            background-position-y: 100%;
            background-position-x: 0%;
            background-repeat: no-repeat;
            transition: background-size 0.3s ease-in-out;
        }

        a:hover,
        a:focus,
        a:active {
            background-size: 100% 0.1em;
        }
    }

    .footer-socials {
        height: 1rem;

        .link {
            color: grey;
            padding: 0;

            .favicon {
                transition: transform 0.5s ease;
                height: 2.8rem;
                width: 2.8rem;
            }

            .favicon:hover {
                text-decoration: none;
                transform: scale(1.3);
                color: var(--accent-primary);
                filter: drop-shadow(0 0 15px var(--shadow-color));
            }
        }
    }
}

hr {
    border-top: 4px solid var(--accent-primary);
    box-shadow: 0 0 10px var(--shadow-color);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.hidden {
    display: none;
}

// Contact Section Styles //

.contact-section {
    div {
        border-radius: 1rem;
    }

    fieldset {
        border-color: grey !important;
        border: 1px;
    }

    .Mui-focused {
        color: #00ff41 !important;
    }

    .text-field {
        background: rgba(26, 26, 46, 0.8);
        border-radius: 1rem;
        border: 1px solid rgba(0, 255, 65, 0.3);
        box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
    }

    .textarea {
        border-radius: 1rem !important;
    }

    .contact-button {
        color: #00ff41 !important;
        border: 2px solid #00ff41 !important;
        font-family: "Orbitron", "JetBrains Mono", monospace !important;
        background: rgba(0, 255, 65, 0.1) !important;
        box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
        transition: all 0.3s ease;
    }

    .contact-button:hover {
        background: #00ff41 !important;
        color: #0a0a0a !important;
        box-shadow: 0 0 25px rgba(0, 255, 65, 0.6) !important;
        transform: translateY(-2px);
    }

    label {
        color: #e0e0e0 !important;
        font-family: "Space Mono", "JetBrains Mono", monospace !important;
        text-shadow: 0 0 5px rgba(224, 224, 224, 0.3);
    }
}

// Optimized Keyframe Animations (removed heavy animations)
// Removed starfield, lightsaber-glow, and force-pulse animations for better performance

// Responsive Design Improvements
@media (max-width: 768px) {
    .App {
        &::before {
            background-size: 400px 300px; // Larger pattern for fewer elements
            opacity: 0.1; // Reduced opacity on mobile
        }

        .name {
            font-size: clamp(1.5rem, 8vw, 4rem) !important;
        }

        .headline {
            font-size: clamp(0.8rem, 4vw, 1.5rem) !important;
            line-height: 1.3;
        }

        .sub-heading {
            font-size: clamp(0.8rem, 4vw, 1.2rem) !important;

            &::after {
                width: 150px;
            }
        }

        .projectGrid {
            gap: 1.5rem !important;

            .project {
                margin: 0.5rem;

                .project-card {
                    width: 90vw;
                    max-width: 350px;
                    margin: 0 auto;
                }
            }
        }

        .skillsGrid .skill {
            height: 2rem;
            width: 2rem;
            margin: 0.5em;
        }

        .lightsaber-cursor-trail {
            display: none; // Hide on mobile for performance
        }

        .intro, .intro-mobile {
            width: 95vw !important;
        }
    }
}

@media (max-width: 480px) {
    .App {
        .name {
            font-size: clamp(1.2rem, 10vw, 3rem) !important;
        }

        .headline {
            font-size: clamp(0.7rem, 5vw, 1.2rem) !important;
        }

        .projectGrid {
            gap: 1rem !important;

            .project {
                margin: 0.25rem;

                .project-card {
                    width: 95vw;
                    max-width: 320px;
                }
            }
        }
    }
}

// Additional Star Wars themed elements
.jedi-glow {
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #00ff41, #FFD700, #00ff41);
        border-radius: inherit;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover::before {
        opacity: 0.7;
    }
}

// Lightsaber cursor effect (optional)
.lightsaber-cursor {
    cursor: none;

    &::after {
        content: '';
        position: fixed;
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #00ff41, transparent);
        border-radius: 2px;
        pointer-events: none;
        z-index: 9999;
        box-shadow: 0 0 10px #00ff41;
    }
}

// Simplified animations for better performance
@keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

// Simple hover effect for enhanced buttons
.enhanced-button {
    transition: background 0.2s ease, color 0.2s ease;

    &:hover {
        background: rgba(0, 255, 65, 0.1);
    }
}
